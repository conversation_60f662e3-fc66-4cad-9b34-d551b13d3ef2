<!DOCTYPE html>
<html>
<head>
    <title>Test slovenských znakov v PDF</title>
    <script src="./jspdf.min.js"></script>
    <script src="./font-loader.js"></script>
</head>
<body>
    <h1>Test slovenských znakov v PDF</h1>
    <button onclick="generateTestPDF()">Generovať test PDF</button>
    
    <div style="margin-top: 20px;">
        <h3>Testované znaky:</h3>
        <p>á, č, ď, é, í, ĺ, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž</p>
        <p>Á, Č, Ď, É, Í, Ĺ, Ľ, Ň, Ó, Ô, Ŕ, Š, Ť, Ú, Ý, Ž</p>
        <p>Testové slová: cenová, ponuka, služby, zákazník, telefón, adresa</p>
        <p>Testové vety: Vytvárame pokojné spomienky. Starostlivosť o hrobové miesta.</p>
    </div>

    <script>
        function generateTestPDF() {
            try {
                const { jsPDF } = window.jsPDF;
                const doc = new jsPDF('p', 'mm', 'a4');
                
                doc.setFont('helvetica');
                
                let yPos = 30;
                
                // Title
                doc.setFontSize(20);
                doc.setTextColor(94, 46, 96);
                doc.text('Test slovenských znakov', 20, yPos);
                yPos += 20;
                
                // Test characters
                doc.setFontSize(14);
                doc.setTextColor(0, 0, 0);
                doc.text('Malé písmená: á, č, ď, é, í, ĺ, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž', 20, yPos);
                yPos += 10;
                
                doc.text('Veľké písmená: Á, Č, Ď, É, Í, Ĺ, Ľ, Ň, Ó, Ô, Ŕ, Š, Ť, Ú, Ý, Ž', 20, yPos);
                yPos += 15;
                
                // Test words
                doc.setFontSize(12);
                doc.text('Testové slová:', 20, yPos);
                yPos += 8;
                
                const testWords = [
                    'cenová ponuka',
                    'služby pre zákazníkov',
                    'telefón a adresa',
                    'medzisúčet a zľava',
                    'informácie o hrobových miestach',
                    'štvrťročná starostlivosť',
                    'sviatočné čistenie',
                    'impregnácia kameňa'
                ];
                
                testWords.forEach(word => {
                    doc.text('• ' + word, 25, yPos);
                    yPos += 6;
                });
                
                yPos += 10;
                
                // Test sentences
                doc.text('Testové vety:', 20, yPos);
                yPos += 8;
                
                const testSentences = [
                    'Vytvárame pokojné spomienky pre vašich blízkych.',
                    'Profesionálna starostlivosť o hrobové miesta.',
                    'Cenová ponuka obsahuje všetky potrebné služby.',
                    'Kontaktujte nás na telefóne +421 951 553 464.',
                    'Naša firma poskytuje kvalitné služby už roky.'
                ];
                
                testSentences.forEach(sentence => {
                    doc.text('• ' + sentence, 25, yPos);
                    yPos += 8;
                });
                
                // Footer
                yPos = 250;
                doc.setFontSize(10);
                doc.setTextColor(107, 114, 128);
                doc.text('Test vygenerovaný: ' + new Date().toLocaleDateString('sk-SK'), 20, yPos);
                
                doc.save('test-slovenske-znaky.pdf');
                alert('Test PDF bol vygenerovaný!');
                
            } catch (error) {
                console.error('Chyba pri generovaní PDF:', error);
                alert('Chyba: ' + error.message);
            }
        }
    </script>
</body>
</html>
