<!DOCTYPE html>
<html>
<head>
    <title>Test pdfMake so slovenský<PERSON> znakmi</title>
    <script src="./pdfmake.min.js"></script>
    <script src="./vfs_fonts.js"></script>
</head>
<body>
    <h1>Test pdfMake so slovenskými znakmi</h1>
    <button onclick="testPDFMake()">Generovať test PDF</button>
    
    <div style="margin-top: 20px;">
        <h3>Testované znaky:</h3>
        <p>á, č, ď, é, í, ĺ, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž</p>
        <p>Á, Č, Ď, É, Í, Ĺ, Ľ, Ň, Ó, Ô, Ŕ, Š, Ť, Ú, Ý, Ž</p>
        <p>Testové slová: cenová, ponuka, služby, zákazník, telefón, adresa</p>
        <p>Testové vety: Vytvárame pokojné spomienky. Starostlivosť o hrobové miesta.</p>
    </div>

    <script>
        function testPDFMake() {
            try {
                console.log('Testing pdfMake...');
                console.log('window.pdfMake:', window.pdfMake);
                
                if (typeof window.pdfMake === 'undefined') {
                    alert('pdfMake is not loaded!');
                    return;
                }
                
                // Initialize fonts
                if (window.pdfFonts) {
                    pdfMake.vfs = pdfFonts.pdfMake.vfs;
                }
                
                const docDefinition = {
                    content: [
                        { text: 'Test slovenských znakov v pdfMake', style: 'header' },
                        { text: 'Malé písmená: á, č, ď, é, í, ĺ, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž', margin: [0, 10, 0, 5] },
                        { text: 'Veľké písmená: Á, Č, Ď, É, Í, Ĺ, Ľ, Ň, Ó, Ô, Ŕ, Š, Ť, Ú, Ý, Ž', margin: [0, 0, 0, 10] },
                        
                        { text: 'Testové slová:', style: 'subheader' },
                        {
                            ul: [
                                'cenová ponuka',
                                'služby pre zákazníkov',
                                'telefón a adresa',
                                'medzisúčet a zľava',
                                'informácie o hrobových miestach',
                                'štvrťročná starostlivosť',
                                'sviatočné čistenie',
                                'impregnácia kameňa'
                            ]
                        },
                        
                        { text: 'Testové vety:', style: 'subheader' },
                        {
                            ol: [
                                'Vytvárame pokojné spomienky pre vašich blízkych.',
                                'Profesionálna starostlivosť o hrobové miesta.',
                                'Cenová ponuka obsahuje všetky potrebné služby.',
                                'Kontaktujte nás na telefóne +421 951 553 464.',
                                'Naša firma poskytuje kvalitné služby už roky.'
                            ]
                        },
                        
                        { text: 'Test tabuľky:', style: 'subheader' },
                        {
                            table: {
                                headerRows: 1,
                                widths: ['*', '*', 'auto'],
                                body: [
                                    ['Služba', 'Typ', 'Cena'],
                                    ['Štvrťročná starostlivosť', 'Urnové miesto', '39,99 €'],
                                    ['Sviatočné čistenie', 'Jednohrob', '59,99 €'],
                                    ['Impregnácia kameňa', 'Dvojhrob', '71,99 €']
                                ]
                            }
                        }
                    ],
                    
                    styles: {
                        header: {
                            fontSize: 18,
                            bold: true,
                            margin: [0, 0, 0, 10]
                        },
                        subheader: {
                            fontSize: 14,
                            bold: true,
                            margin: [0, 10, 0, 5]
                        }
                    }
                };
                
                pdfMake.createPdf(docDefinition).download('test-slovenske-znaky-pdfmake.pdf');
                alert('PDF generated successfully with pdfMake!');
                
            } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
