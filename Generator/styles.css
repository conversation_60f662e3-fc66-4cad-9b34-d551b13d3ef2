/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e293b;
    line-height: 1.6;
    min-height: 100vh;
}

/* Color Variables */
:root {
    --primary-color: #5e2e60;
    --secondary-color: #5f8132;
    --tertiary-color: #327881;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --border-color: #e2e8f0;
    --text-muted: #64748b;
    --white: #ffffff;
}

.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    color: var(--white);
    padding: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
.main {
    flex: 1;
    padding: 3rem 0;
}

/* Form Sections */
.form-section {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color);
}

.form-section h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    margin-bottom: 0;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Price Toggle */
.price-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Grave Items */
.grave-item {
    background: var(--light-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.2s ease;
}

.grave-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.1);
}

.grave-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.grave-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.btn-remove {
    background: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-remove:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    padding: 1rem;
}

.price-display .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.price-display small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Buttons */
.btn-add {
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem auto;
}

.btn-add:hover {
    background: #4f6b28;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(95, 129, 50, 0.3);
}

.btn-generate {
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    color: var(--white);
    border: none;
    padding: 1.25rem 2.5rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 2rem auto;
    box-shadow: 0 4px 12px rgba(94, 46, 96, 0.3);
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(94, 46, 96, 0.4);
}

.btn-generate:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Additional Services */
.additional-services {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--light-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-item:hover {
    border-color: var(--primary-color);
    background: var(--white);
}

.checkbox-item input[type="checkbox"] {
    margin-right: 1rem;
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: var(--primary-color);
}

.service-price {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 1.1rem;
}

/* Discount Section */
.discount-section {
    max-width: 300px;
}

/* Summary Section */
.summary-section {
    background: linear-gradient(135deg, var(--white), var(--light-bg));
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
}

.total-price {
    text-align: center;
    margin-bottom: 2rem;
}

.price-breakdown {
    margin-bottom: 1.5rem;
}

.subtotal,
.discount-amount {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 1rem;
}

.discount-value {
    color: var(--danger-color);
    font-weight: 600;
}

.total h2 {
    color: var(--primary-color);
    font-size: 2rem;
    margin: 1rem 0 0.5rem 0;
}

.total p {
    color: var(--text-muted);
    font-size: 1rem;
}

.tax-breakdown {
    background: var(--light-bg);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.tax-breakdown div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

/* Footer */
.footer {
    background: var(--primary-color);
    color: var(--white);
    text-align: center;
    padding: 2rem 0;
    margin-top: auto;
}

.footer p {
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .grave-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .btn-generate {
        width: 100%;
        justify-content: center;
    }
}
