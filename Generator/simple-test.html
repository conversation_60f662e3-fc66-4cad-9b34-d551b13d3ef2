<!DOCTYPE html>
<html>
<head>
    <title>Simple jsPDF Test</title>
    <script src="./jspdf.min.js"></script>
</head>
<body>
    <h1>Simple jsPDF Test</h1>
    <button onclick="testPDF()">Generate Simple PDF</button>
    
    <script>
        function testPDF() {
            console.log('Testing jsPDF...');
            console.log('window.jsPDF:', window.jsPDF);
            
            try {
                if (typeof window.jsPDF === 'undefined') {
                    alert('jsPDF is not loaded!');
                    return;
                }
                
                const { jsPDF } = window.jsPDF;
                console.log('jsPDF constructor:', jsPDF);
                
                const doc = new jsPDF();
                console.log('Document created:', doc);
                
                doc.text('Hello World!', 10, 10);
                doc.text('This is a test PDF', 10, 20);
                doc.text('Generated with jsPDF', 10, 30);
                
                doc.save('simple-test.pdf');
                alert('PDF generated successfully!');
                
            } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
