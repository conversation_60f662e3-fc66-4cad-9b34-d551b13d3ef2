// Font loader for jsPDF with Slovak character support
console.log('Loading Slovak font support...');

// Wait for jsPDF to be available
function initSlovakSupport() {
    if (typeof window.jsPDF === 'undefined') {
        console.log('jsPDF not yet available, retrying...');
        setTimeout(initSlovakSupport, 100);
        return;
    }

    console.log('jsPD<PERSON> found, initializing Slovak support...');

    // Store original jsPDF constructor
    const OriginalJsPDF = window.jsPDF;

    // Create new constructor that adds Slovak support
    window.jsPDF = function(...args) {
        console.log('Creating jsPDF instance with Slovak support...');
        const doc = new OriginalJsPDF(...args);

        // Override the text method to handle Slovak characters
        const originalText = doc.text;
        doc.text = function(text, x, y, options) {
            if (typeof text === 'string') {
                // Simple character replacement for better compatibility
                text = text
                    .replace(/á/g, 'a').replace(/Á/g, 'A')
                    .replace(/č/g, 'c').replace(/Č/g, 'C')
                    .replace(/ď/g, 'd').replace(/Ď/g, 'D')
                    .replace(/é/g, 'e').replace(/É/g, 'E')
                    .replace(/í/g, 'i').replace(/Í/g, 'I')
                    .replace(/ĺ/g, 'l').replace(/Ĺ/g, 'L')
                    .replace(/ľ/g, 'l').replace(/Ľ/g, 'L')
                    .replace(/ň/g, 'n').replace(/Ň/g, 'N')
                    .replace(/ó/g, 'o').replace(/Ó/g, 'O')
                    .replace(/ô/g, 'o').replace(/Ô/g, 'O')
                    .replace(/ŕ/g, 'r').replace(/Ŕ/g, 'R')
                    .replace(/š/g, 's').replace(/Š/g, 'S')
                    .replace(/ť/g, 't').replace(/Ť/g, 'T')
                    .replace(/ú/g, 'u').replace(/Ú/g, 'U')
                    .replace(/ý/g, 'y').replace(/Ý/g, 'Y')
                    .replace(/ž/g, 'z').replace(/Ž/g, 'Z');
            }
            return originalText.call(this, text, x, y, options);
        };

        console.log('Slovak support added to jsPDF instance');
        return doc;
    };

    // Copy all static properties and methods
    Object.setPrototypeOf(window.jsPDF, OriginalJsPDF);
    Object.assign(window.jsPDF, OriginalJsPDF);

    console.log('Slovak font support initialized successfully');
}

// Start initialization
initSlovakSupport();
