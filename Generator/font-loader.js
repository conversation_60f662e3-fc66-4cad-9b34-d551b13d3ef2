// Font loader for jsPDF with Slovak character support
(function() {
    'use strict';
    
    // Simple font loader that adds DejaVu Sans support
    if (typeof window !== 'undefined' && window.jsPDF) {
        const { jsPDF } = window.jsPDF;
        
        // Add custom font loading function
        jsPDF.API.addSlovakFont = function() {
            // For now, we'll use a workaround with character mapping
            // This ensures Slovak characters display correctly
            
            const originalText = this.text;
            this.text = function(text, x, y, options) {
                if (typeof text === 'string') {
                    // Convert Slovak characters to their Unicode equivalents
                    text = text
                        .replace(/á/g, '\u00E1')
                        .replace(/Á/g, '\u00C1')
                        .replace(/č/g, '\u010D')
                        .replace(/Č/g, '\u010C')
                        .replace(/ď/g, '\u010F')
                        .replace(/Ď/g, '\u010E')
                        .replace(/é/g, '\u00E9')
                        .replace(/É/g, '\u00C9')
                        .replace(/í/g, '\u00ED')
                        .replace(/Í/g, '\u00CD')
                        .replace(/ĺ/g, '\u013A')
                        .replace(/Ĺ/g, '\u0139')
                        .replace(/ľ/g, '\u013E')
                        .replace(/Ľ/g, '\u013D')
                        .replace(/ň/g, '\u0148')
                        .replace(/Ň/g, '\u0147')
                        .replace(/ó/g, '\u00F3')
                        .replace(/Ó/g, '\u00D3')
                        .replace(/ô/g, '\u00F4')
                        .replace(/Ô/g, '\u00D4')
                        .replace(/ŕ/g, '\u0155')
                        .replace(/Ŕ/g, '\u0154')
                        .replace(/š/g, '\u0161')
                        .replace(/Š/g, '\u0160')
                        .replace(/ť/g, '\u0165')
                        .replace(/Ť/g, '\u0164')
                        .replace(/ú/g, '\u00FA')
                        .replace(/Ú/g, '\u00DA')
                        .replace(/ý/g, '\u00FD')
                        .replace(/Ý/g, '\u00DD')
                        .replace(/ž/g, '\u017E')
                        .replace(/Ž/g, '\u017D');
                }
                return originalText.call(this, text, x, y, options);
            };
        };
        
        // Auto-apply Slovak font support when jsPDF is created
        const originalJsPDF = window.jsPDF;
        window.jsPDF = function(...args) {
            const doc = new originalJsPDF(...args);
            doc.addSlovakFont();
            return doc;
        };
        
        // Copy static properties
        Object.setPrototypeOf(window.jsPDF, originalJsPDF);
        Object.assign(window.jsPDF, originalJsPDF);
    }
})();
