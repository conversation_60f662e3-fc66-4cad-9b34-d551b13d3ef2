<!DOCTYPE html>
<html>
<head>
    <title>Font Converter for jsPDF</title>
    <script src="./jspdf.min.js"></script>
</head>
<body>
    <h1>Font Converter</h1>
    <input type="file" id="fontFile" accept=".ttf" />
    <button onclick="convertFont()">Convert Font</button>
    <div id="output"></div>

    <script>
        async function convertFont() {
            const fileInput = document.getElementById('fontFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a TTF file');
                return;
            }

            try {
                const arrayBuffer = await file.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);
                
                // Convert to base64
                let binary = '';
                for (let i = 0; i < uint8Array.byteLength; i++) {
                    binary += String.fromCharCode(uint8Array[i]);
                }
                const base64 = btoa(binary);
                
                // Create font definition
                const fontName = file.name.replace('.ttf', '');
                const fontDefinition = `
// Font: ${fontName}
(function (jsPDFAPI) {
    "use strict";
    
    var font = '${base64}';
    
    jsPDFAPI.addFileToVFS('${fontName}.ttf', font);
    jsPDFAPI.addFont('${fontName}.ttf', '${fontName}', 'normal');
    
})(jsPDF.API);
`;

                // Display result
                document.getElementById('output').innerHTML = 
                    '<h3>Font converted successfully!</h3>' +
                    '<p>Copy this code and save it as ' + fontName + '.js:</p>' +
                    '<textarea style="width:100%; height:200px;">' + fontDefinition + '</textarea>';
                    
            } catch (error) {
                console.error('Error converting font:', error);
                alert('Error converting font: ' + error.message);
            }
        }
    </script>
</body>
</html>
