<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test jsPDF</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #5e2e60;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #7c3aed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test jsPDF knižnice</h1>
        <p>Tento test overuje, či sa jsPDF knižnica načítava správne.</p>
        
        <button class="btn" onclick="checkLibrary()">Skontrolovať knižnicu</button>
        <button class="btn" onclick="generateTestPDF()">Generovať test PDF</button>
        
        <div id="status"></div>
    </div>

    <script>
        function checkLibrary() {
            const status = document.getElementById('status');

            console.log('window.jsPDF:', typeof window.jsPDF);

            if (typeof window.jsPDF !== 'undefined') {
                status.innerHTML = '<div class="success">✅ jsPDF knižnica je načítaná správne (window.jsPDF)</div>';
                return true;
            } else {
                status.innerHTML = '<div class="error">❌ jsPDF knižnica nie je načítaná!</div>';
                return false;
            }
        }

        function generateTestPDF() {
            const status = document.getElementById('status');

            if (!checkLibrary()) {
                return;
            }

            try {
                // Create new PDF document
                const doc = new window.jsPDF();
                
                // Add content
                doc.setFontSize(20);
                doc.setTextColor(94, 46, 96);
                doc.text('eHroby - Test PDF', 20, 30);
                
                doc.setFontSize(12);
                doc.setTextColor(0, 0, 0);
                doc.text('Tento PDF bol vygenerovaný pomocou jsPDF knižnice.', 20, 50);
                doc.text('Slovenské znaky: áéíóúýčďľňšťž', 20, 65);
                doc.text('Dátum: ' + new Date().toLocaleDateString('sk-SK'), 20, 80);
                
                // Save PDF
                doc.save('test-ehroby.pdf');
                
                status.innerHTML = '<div class="success">✅ Test PDF bol úspešne vygenerovaný!</div>';
                
            } catch (error) {
                console.error('Error generating PDF:', error);
                status.innerHTML = '<div class="error">❌ Chyba pri generovaní PDF: ' + error.message + '</div>';
            }
        }
        
        // Auto-check on page load
        window.addEventListener('load', function() {
            setTimeout(checkLibrary, 500);
        });
    </script>
</body>
</html>
