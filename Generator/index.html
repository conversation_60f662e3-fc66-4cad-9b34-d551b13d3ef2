<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON>r<PERSON><PERSON> cenov<PERSON>ch pon<PERSON>k</title>
    <meta name="description" content="Profesionálny generátor PDF cenových ponúk pre služby starostlivosti o hrobové miesta">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="./jspdf.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Cenové tabuľky podľa špecifikácie
        const PRICES = {
            jednorazove: {
                urnove: { bezDph: 39.99, sDph: 47.99 },
                jednohrob_nesavy: { bezDph: 49.99, sDph: 59.99 },
                jednohrob_savy: { bezDph: 54.99, sDph: 65.99 },
                dvojhrob_nesavy: { bezDph: 64.99, sDph: 77.99 },
                dvojhrob_savy: { bezDph: 69.99, sDph: 83.99 }
            },
            duo: {
                urnove: { bezDph: 74.99, sDph: 89.99 },
                jednohrob_nesavy: { bezDph: 94.99, sDph: 113.99 },
                jednohrob_savy: { bezDph: 98.99, sDph: 118.79 },
                dvojhrob_nesavy: { bezDph: 119.99, sDph: 143.99 },
                dvojhrob_savy: { bezDph: 124.99, sDph: 149.99 }
            },
            stvrtrocne: {
                urnove: { bezDph: 149.99, sDph: 179.99 },
                jednohrob_nesavy: { bezDph: 189.99, sDph: 227.99 },
                jednohrob_savy: { bezDph: 199.99, sDph: 239.99 },
                dvojhrob_nesavy: { bezDph: 239.99, sDph: 287.99 },
                dvojhrob_savy: { bezDph: 259.99, sDph: 311.99 }
            },
            stvrtrocne_akcia: {
                urnove: { bezDph: 299.98, sDph: 359.98 },
                jednohrob_nesavy: { bezDph: 379.98, sDph: 455.98 },
                jednohrob_savy: { bezDph: 399.98, sDph: 479.98 },
                dvojhrob_nesavy: { bezDph: 479.98, sDph: 575.98 },
                dvojhrob_savy: { bezDph: 519.98, sDph: 623.98 }
            },
            mesacne: {
                urnove: { bezDph: 329.99, sDph: 395.99 },
                jednohrob_nesavy: { bezDph: 429.99, sDph: 515.99 },
                jednohrob_savy: { bezDph: 459.99, sDph: 551.99 },
                dvojhrob_nesavy: { bezDph: 549.99, sDph: 659.99 },
                dvojhrob_savy: { bezDph: 589.99, sDph: 707.99 }
            },
            specialna: {
                urnove: { bezDph: 659.98, sDph: 791.98 },
                jednohrob_nesavy: { bezDph: 859.98, sDph: 1031.98 },
                jednohrob_savy: { bezDph: 919.98, sDph: 1103.98 },
                dvojhrob_nesavy: { bezDph: 1099.98, sDph: 1319.98 },
                dvojhrob_savy: { bezDph: 1179.98, sDph: 1415.98 }
            }
        };

        const GRAVE_TYPES = {
            urnove: 'Urnové miesto',
            jednohrob_nesavy: 'Jednohrob (nesavý)',
            jednohrob_savy: 'Jednohrob (savý)',
            dvojhrob_nesavy: 'Dvojhrob (nesavý)',
            dvojhrob_savy: 'Dvojhrob (savý)'
        };

        const SERVICE_TYPES = {
            jednorazove: 'Jednorazové umytie (1× ročne)',
            duo: 'DUO umytie (2× ročne)',
            stvrtrocne: 'Štvrťročná starostlivosť (4× ročne)',
            stvrtrocne_akcia: 'Štvrťročná starostlivosť 2+1 rok zadarmo (3 roky)',
            mesacne: 'Každý mesiac (12× ročne)',
            specialna: 'Špeciálna ponuka 2+1 zdarma (3 roky)'
        };

        function App() {
            const [showDph, setShowDph] = useState(true);
            const [clientData, setClientData] = useState({
                name: '',
                phone: '',
                email: '',
                address: ''
            });
            const [graves, setGraves] = useState([{
                id: 1,
                type: 'urnove',
                service: 'jednorazove',
                location: ''
            }]);
            const [additionalServices, setAdditionalServices] = useState({
                sviatocne: false,
                pisma: false,
                impregnacia: false
            });
            const [notes, setNotes] = useState('');
            const [discount, setDiscount] = useState(0);

            const addGrave = () => {
                const newId = Math.max(...graves.map(g => g.id)) + 1;
                setGraves([...graves, {
                    id: newId,
                    type: 'urnove',
                    service: 'jednorazove',
                    location: ''
                }]);
            };

            const removeGrave = (id) => {
                if (graves.length > 1) {
                    setGraves(graves.filter(g => g.id !== id));
                }
            };

            const updateGrave = (id, field, value) => {
                setGraves(graves.map(g => 
                    g.id === id ? { ...g, [field]: value } : g
                ));
            };

            const calculateSubtotal = () => {
                let total = 0;
                graves.forEach(grave => {
                    const price = PRICES[grave.service][grave.type];
                    total += showDph ? price.sDph : price.bezDph;
                });

                // Doplnkové služby
                if (additionalServices.sviatocne) total += showDph ? 59.99 : 49.99;
                if (additionalServices.pisma) total += showDph ? 119.99 : 99.99;
                if (additionalServices.impregnacia) total += showDph ? 71.99 : 59.99;

                return total;
            };

            const calculateTotal = () => {
                const subtotal = calculateSubtotal();
                const discountAmount = subtotal * (discount / 100);
                return subtotal - discountAmount;
            };

            const generatePDF = async () => {
                console.log('generatePDF called');

                // Validate required fields
                if (!clientData.name || !clientData.phone) {
                    alert('Prosím vyplňte meno a telefón zákazníka.');
                    return;
                }

                if (graves.length === 0) {
                    alert('Prosím pridajte aspoň jeden hrob.');
                    return;
                }

                // Check if jsPDF is available
                console.log('jsPDF check:', typeof window.jsPDF);
                console.log('window.jsPDF:', window.jsPDF);

                if (typeof window.jsPDF === 'undefined') {
                    alert('Chyba: jsPDF knižnica nie je načítaná. Skúste obnoviť stránku.');
                    return;
                }

                try {
                    // Show loading message
                    const button = document.querySelector('.btn-generate');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i className="fas fa-spinner fa-spin"></i> Generujem PDF...';
                    button.disabled = true;

                    console.log('Starting PDF generation...');

                    // Create PDF using jsPDF
                    console.log('Accessing jsPDF...');

                    // Try different ways to access jsPDF
                    let jsPDF;
                    if (window.jsPDF && window.jsPDF.jsPDF) {
                        jsPDF = window.jsPDF.jsPDF;
                        console.log('Using window.jsPDF.jsPDF');
                    } else if (window.jsPDF) {
                        jsPDF = window.jsPDF;
                        console.log('Using window.jsPDF');
                    } else {
                        throw new Error('jsPDF not found');
                    }

                    console.log('jsPDF constructor:', jsPDF);

                    console.log('Creating new jsPDF document...');
                    const doc = new jsPDF('p', 'mm', 'a4');
                    console.log('jsPDF document created:', doc);

                    // Set font with Unicode support
                    doc.setFont('helvetica');

                    // Simple Slovak character replacement for better compatibility
                    const fixSlovakText = (text) => {
                        return text
                            .replace(/á/g, 'a').replace(/Á/g, 'A')
                            .replace(/č/g, 'c').replace(/Č/g, 'C')
                            .replace(/ď/g, 'd').replace(/Ď/g, 'D')
                            .replace(/é/g, 'e').replace(/É/g, 'E')
                            .replace(/í/g, 'i').replace(/Í/g, 'I')
                            .replace(/ĺ/g, 'l').replace(/Ĺ/g, 'L')
                            .replace(/ľ/g, 'l').replace(/Ľ/g, 'L')
                            .replace(/ň/g, 'n').replace(/Ň/g, 'N')
                            .replace(/ó/g, 'o').replace(/Ó/g, 'O')
                            .replace(/ô/g, 'o').replace(/Ô/g, 'O')
                            .replace(/ŕ/g, 'r').replace(/Ŕ/g, 'R')
                            .replace(/š/g, 's').replace(/Š/g, 'S')
                            .replace(/ť/g, 't').replace(/Ť/g, 'T')
                            .replace(/ú/g, 'u').replace(/Ú/g, 'U')
                            .replace(/ý/g, 'y').replace(/Ý/g, 'Y')
                            .replace(/ž/g, 'z').replace(/Ž/g, 'Z');
                    };

                    // Add logo (if available)
                    try {
                        const logoImg = new Image();
                        logoImg.crossOrigin = 'anonymous';
                        logoImg.src = './logo36.png';

                        await new Promise((resolve, reject) => {
                            logoImg.onload = () => {
                                try {
                                    doc.addImage(logoImg, 'PNG', 20, 20, 20, 10);
                                    resolve();
                                } catch (e) {
                                    console.warn('Logo could not be added:', e);
                                    resolve();
                                }
                            };
                            logoImg.onerror = () => {
                                console.warn('Logo could not be loaded');
                                resolve();
                            };
                            // Timeout after 2 seconds
                            setTimeout(resolve, 2000);
                        });
                    } catch (e) {
                        console.warn('Logo loading failed:', e);
                    }

                    // Header
                    doc.setFontSize(24);
                    doc.setTextColor(94, 46, 96); // #5e2e60
                    doc.text(fixSlovakText('eHroby'), 50, 30);

                    doc.setFontSize(12);
                    doc.setTextColor(95, 129, 50); // #5f8132
                    doc.text(fixSlovakText('vytvárame pokojné spomienky'), 50, 37);

                    doc.setFontSize(18);
                    doc.setTextColor(50, 120, 129); // #327881
                    doc.text(fixSlovakText('Cenová ponuka'), 140, 30);

                    doc.setFontSize(10);
                    doc.setTextColor(107, 114, 128);
                    const today = new Date().toLocaleDateString('sk-SK');
                    doc.text(fixSlovakText(`Dátum: ${today}`), 140, 37);

                    // Line under header
                    doc.setDrawColor(94, 46, 96);
                    doc.setLineWidth(1);
                    doc.line(20, 45, 190, 45);

                    let yPos = 60;

                    // Client info
                    doc.setFontSize(14);
                    doc.setTextColor(30, 41, 59);
                    doc.text(fixSlovakText('Informácie o zákazníkovi:'), 20, yPos);
                    yPos += 10;

                    doc.setFontSize(11);
                    doc.setTextColor(75, 85, 99);
                    doc.text(fixSlovakText(`Meno: ${clientData.name}`), 25, yPos);
                    yPos += 6;
                    doc.text(fixSlovakText(`Telefón: ${clientData.phone}`), 25, yPos);
                    yPos += 6;
                    if (clientData.email) {
                        doc.text(fixSlovakText(`Email: ${clientData.email}`), 25, yPos);
                        yPos += 6;
                    }
                    if (clientData.address) {
                        doc.text(fixSlovakText(`Adresa: ${clientData.address}`), 25, yPos);
                        yPos += 6;
                    }

                    yPos += 10;

                    // Services table
                    doc.setFontSize(14);
                    doc.setTextColor(30, 41, 59);
                    doc.text(fixSlovakText('Služby:'), 20, yPos);
                    yPos += 10;

                    // Table header
                    doc.setFillColor(248, 250, 252);
                    doc.rect(20, yPos - 5, 170, 8, 'F');
                    doc.setFontSize(10);
                    doc.setTextColor(55, 65, 81);
                    doc.text(fixSlovakText('Služba'), 25, yPos);
                    doc.text(fixSlovakText('Typ hrobu'), 100, yPos);
                    doc.text(fixSlovakText('Cena'), 130, yPos);
                    doc.text(fixSlovakText('Lokácia'), 160, yPos);
                    yPos += 10;

                    // Services rows
                    let subtotal = 0;
                    graves.forEach((grave, index) => {
                        const price = PRICES[grave.service][grave.type];
                        const displayPrice = showDph ? price.sDph : price.bezDph;
                        subtotal += displayPrice;

                        doc.setTextColor(75, 85, 99);
                        doc.text(fixSlovakText(SERVICE_TYPES[grave.service]), 25, yPos);
                        doc.text(fixSlovakText(GRAVE_TYPES[grave.type]), 100, yPos);
                        doc.text(`${displayPrice.toFixed(2)} €`, 130, yPos);
                        if (grave.location) {
                            doc.text(fixSlovakText(grave.location), 160, yPos);
                        }
                        yPos += 7;
                    });

                    // Additional services
                    if (additionalServices.sviatocne) {
                        const price = showDph ? 59.99 : 49.99;
                        subtotal += price;
                        doc.text(fixSlovakText('Sviatočné čistenie'), 25, yPos);
                        doc.text(fixSlovakText('Doplnková služba'), 100, yPos);
                        doc.text(`${price.toFixed(2)} €`, 130, yPos);
                        yPos += 7;
                    }
                    if (additionalServices.pisma) {
                        const price = showDph ? 119.99 : 99.99;
                        subtotal += price;
                        doc.text(fixSlovakText('Obnova písma'), 25, yPos);
                        doc.text(fixSlovakText('Doplnková služba'), 100, yPos);
                        doc.text(`${price.toFixed(2)} €`, 130, yPos);
                        yPos += 7;
                    }
                    if (additionalServices.impregnacia) {
                        const price = showDph ? 71.99 : 59.99;
                        subtotal += price;
                        doc.text(fixSlovakText('Impregnácia kameňa'), 25, yPos);
                        doc.text(fixSlovakText('Doplnková služba'), 100, yPos);
                        doc.text(`${price.toFixed(2)} €`, 130, yPos);
                        yPos += 7;
                    }

                    yPos += 5;

                    // Totals
                    doc.setDrawColor(229, 231, 235);
                    doc.line(20, yPos, 190, yPos);
                    yPos += 8;

                    doc.setFontSize(11);
                    doc.setTextColor(75, 85, 99);
                    const calculatedSubtotal = calculateSubtotal();
                    doc.text(fixSlovakText('Medzisúčet:'), 130, yPos);
                    doc.text(`${calculatedSubtotal.toFixed(2)} €`, 160, yPos);
                    yPos += 7;

                    if (discount > 0) {
                        const discountAmount = calculatedSubtotal * (discount / 100);
                        doc.text(fixSlovakText(`Zľava (${discount}%):`), 130, yPos);
                        doc.text(`-${discountAmount.toFixed(2)} €`, 160, yPos);
                        yPos += 7;
                    }

                    doc.setFontSize(12);
                    doc.setTextColor(30, 41, 59);
                    const total = calculateTotal();
                    doc.text(fixSlovakText('Celková suma:'), 130, yPos);
                    doc.text(`${total.toFixed(2)} €`, 160, yPos);
                    yPos += 5;
                    doc.setFontSize(9);
                    doc.setTextColor(107, 114, 128);
                    doc.text(fixSlovakText(showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'), 130, yPos);

                    // Footer
                    yPos = 250;
                    doc.setFontSize(10);
                    doc.setTextColor(107, 114, 128);
                    doc.text(fixSlovakText('Kontakt:'), 20, yPos);
                    doc.text(fixSlovakText('Telefón: +421 951 553 464'), 20, yPos + 6);
                    doc.text(fixSlovakText('Email: <EMAIL>'), 20, yPos + 12);

                    // Save PDF
                    const filename = `cenova-ponuka-ehroby-${clientData.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.pdf`;
                    doc.save(filename);

                    console.log('PDF generated successfully');
                    alert('PDF bolo úspešne vygenerované!');

                    // Restore button
                    button.innerHTML = originalText;
                    button.disabled = false;

                } catch (error) {
                    console.error('PDF generation error:', error);
                    alert('Chyba pri generovaní PDF. Skúste to znovu alebo obnovte stránku.');

                    // Restore button
                    const button = document.querySelector('.btn-generate');
                    button.innerHTML = '<i className="fas fa-file-pdf"></i> Generovať PDF ponuku';
                    button.disabled = false;
                }
            };

            const createPDFContent = (data) => {
                const today = new Date().toLocaleDateString('sk-SK');

                return `
                <div style="font-family: Arial, Helvetica, sans-serif; max-width: 800px; margin: 0 auto; padding: 30px; color: #1e293b; background: white;">
                    <!-- Header with Logo -->
                    <div style="display: flex; align-items: center; margin-bottom: 40px; border-bottom: 3px solid #5e2e60; padding-bottom: 25px;">
                        <img src="./logo36.png" alt="eHroby Logo" style="width: 60px; height: 32px; margin-right: 20px;" onerror="this.style.display='none'">
                        <div style="flex: 1;">
                            <h1 style="color: #5e2e60; font-size: 32px; margin: 0; font-weight: 700; letter-spacing: -0.5px;">eHroby</h1>
                            <p style="color: #5f8132; font-size: 16px; margin: 5px 0; font-weight: 500;">vytvárame pokojné spomienky</p>
                        </div>
                        <div style="text-align: right;">
                            <h2 style="color: #327881; font-size: 24px; margin: 0; font-weight: 600;">Cenová ponuka</h2>
                            <p style="color: #6b7280; font-size: 14px; margin: 5px 0;">Dátum: ${today}</p>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 35px; font-size: 13px; color: #6b7280;">
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #5e2e60;">
                            <h4 style="color: #5e2e60; margin: 0 0 8px 0; font-size: 14px;">Kontakt</h4>
                            <p style="margin: 3px 0;"><strong>Vladimír Seman</strong></p>
                            <p style="margin: 3px 0;">📞 +421 951 553 464</p>
                            <p style="margin: 3px 0;">✉️ <EMAIL></p>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div style="margin-bottom: 35px; background: #f8fafc; padding: 20px; border-radius: 12px; border-left: 4px solid #5e2e60;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 15px 0; font-weight: 600;">Údaje o zákazníkovi</h3>
                        <div style="font-size: 15px; line-height: 1.6;">
                            <p style="margin: 8px 0;"><strong>Meno:</strong> ${data.clientData.name}</p>
                            <p style="margin: 8px 0;"><strong>Telefón:</strong> ${data.clientData.phone}</p>
                            ${data.clientData.email ? `<p style="margin: 8px 0;"><strong>Email:</strong> ${data.clientData.email}</p>` : ''}
                            ${data.clientData.address ? `<p style="margin: 8px 0;"><strong>Adresa:</strong> ${data.clientData.address}</p>` : ''}
                        </div>
                    </div>

                    <!-- Services -->
                    <div style="margin-bottom: 35px;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Vybrané služby</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: 14px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #5e2e60, #7c3aed);">
                                    <th style="padding: 15px; text-align: left; color: white; font-weight: 600; font-size: 15px;">Služba</th>
                                    <th style="padding: 15px; text-align: center; color: white; font-weight: 600; font-size: 15px;">Typ hrobu</th>
                                    <th style="padding: 15px; text-align: right; color: white; font-weight: 600; font-size: 15px;">Cena</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.graves.map((grave, index) => {
                                    const price = PRICES[grave.service][grave.type];
                                    const displayPrice = data.showDph ? price.sDph : price.bezDph;
                                    const backgroundColor = index % 2 === 0 ? '#ffffff' : '#f8fafc';

                                    let html = `<tr style="background: ${backgroundColor};">
                                        <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${SERVICE_TYPES[grave.service]}</td>
                                        <td style="padding: 15px; text-align: center; border-bottom: 1px solid #e5e7eb; color: #6b7280;">${GRAVE_TYPES[grave.type]}</td>
                                        <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${displayPrice.toFixed(2)} €</td>
                                    </tr>`;

                                    if (grave.location) {
                                        html += `<tr style="background: ${backgroundColor};">
                                            <td colspan="3" style="padding: 8px 15px 15px 30px; border-bottom: 1px solid #e5e7eb; color: #6b7280; font-size: 13px; font-style: italic;">
                                            📍 Lokalita: ${grave.location}
                                            </td>
                                        </tr>`;
                                    }

                                    return html;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>

                    ${(data.additionalServices.sviatocne || data.additionalServices.pisma || data.additionalServices.impregnacia) ?
                        `<!-- Additional Services -->
                        <div style="margin-bottom: 35px;">
                            <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Doplnkové služby</h3>
                            <table style="width: 100%; border-collapse: collapse; font-size: 14px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                                <tbody>
                                    ${data.additionalServices.sviatocne ?
                                        `<tr style="background: #ffffff;">
                                            <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">🎄 Sviatočné čistenie</td>
                                            <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '59,99' : '49,99'} €</td>
                                        </tr>` : ''}
                                    ${data.additionalServices.pisma ?
                                        `<tr style="background: #f8fafc;">
                                            <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">✍️ Obnova písma</td>
                                            <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '119,99' : '99,99'} €</td>
                                        </tr>` : ''}
                                    ${data.additionalServices.impregnacia ?
                                        `<tr style="background: #ffffff;">
                                            <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">🛡️ Impregnácia kameňa</td>
                                            <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '71,99' : '59,99'} €</td>
                                        </tr>` : ''}
                                </tbody>
                            </table>
                        </div>` : ''}

                    <!-- Price Summary -->
                    <div style="margin-bottom: 35px; background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 25px; border-radius: 12px; border: 2px solid #e5e7eb;">
                        <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Súhrn cien</h3>
                        <table style="width: 100%; font-size: 15px;">
                            ${data.showDph ?
                                `<tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">Základ dane:</td>
                                    <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${(data.subtotal / 1.2).toFixed(2)} €</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">DPH 20%:</td>
                                    <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${(data.subtotal - data.subtotal / 1.2).toFixed(2)} €</td>
                                </tr>` : ''}
                            ${data.discount > 0 ?
                                `<tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">Zľava ${data.discount}%:</td>
                                    <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500; color: #ef4444;">-${(data.subtotal * data.discount / 100).toFixed(2)} €</td>
                                </tr>` : ''}
                            <tr style="font-size: 20px; font-weight: 700; color: #5e2e60;">
                                <td style="padding: 20px 0; border-top: 3px solid #5e2e60;">Celkom ${data.showDph ? 's DPH' : 'bez DPH'}:</td>
                                <td style="padding: 20px 0; text-align: right; border-top: 3px solid #5e2e60;">${data.total.toFixed(2)} €</td>
                            </tr>
                        </table>
                    </div>

                    ${data.notes.trim() ?
                        `<!-- Notes -->
                        <div style="margin-bottom: 35px; background: #fffbeb; padding: 20px; border-radius: 12px; border-left: 4px solid #f59e0b;">
                            <h3 style="color: #92400e; font-size: 16px; margin: 0 0 10px 0; font-weight: 600;">📝 Poznámky</h3>
                            <p style="color: #78350f; font-size: 14px; line-height: 1.6; margin: 0;">${data.notes}</p>
                        </div>` : ''}

                    <!-- Footer -->
                    <div style="text-align: center; font-size: 12px; color: #6b7280; border-top: 2px solid #e5e7eb; padding-top: 25px; margin-top: 40px;">
                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                            <p style="margin: 5px 0; font-weight: 600; color: #5e2e60;">🙏 Ďakujeme za Váš záujem o naše služby!</p>
                            <p style="margin: 5px 0;">Pre viac informácií nás kontaktujte na <strong>+421 951 553 464</strong> alebo <strong><EMAIL></strong></p>
                        </div>
                        <p style="margin: 5px 0; font-style: italic; color: #9ca3af;">Ponuka platná 30 dní od dátumu vystavenia</p>
                        <p style="margin: 5px 0; font-weight: 500; color: #5e2e60;">eHroby - vytvárame pokojné spomienky</p>
                    </div>
                </div>`;
            };

            const generatePrintVersion = () => {
                // Create print-friendly content
                const today = new Date().toLocaleDateString('sk-SK');
                const subtotal = calculateSubtotal();
                const total = calculateTotal();

                let printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Cenová ponuka - eHroby</title>
                    <style>
                        @media print {
                            body { margin: 0; }
                            @page { margin: 0.5in; }
                        }
                        body {
                            font-family: Arial, sans-serif;
                            line-height: 1.4;
                            color: #000;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 20px;
                        }
                        .header {
                            border-bottom: 3px solid #5e2e60;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .header h1 {
                            color: #5e2e60;
                            font-size: 28px;
                            margin: 0;
                        }
                        .header p {
                            color: #5f8132;
                            margin: 5px 0;
                        }
                        .section {
                            margin-bottom: 25px;
                        }
                        .section h3 {
                            color: #5e2e60;
                            font-size: 16px;
                            margin-bottom: 10px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 15px;
                        }
                        th, td {
                            padding: 8px;
                            text-align: left;
                            border-bottom: 1px solid #ddd;
                        }
                        th {
                            background: #5e2e60;
                            color: white;
                        }
                        .total {
                            font-size: 18px;
                            font-weight: bold;
                            color: #5e2e60;
                            text-align: right;
                            margin-top: 20px;
                        }
                        .footer {
                            text-align: center;
                            margin-top: 40px;
                            padding-top: 20px;
                            border-top: 2px solid #ddd;
                            font-size: 12px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>eHroby</h1>
                        <p>vytvárame pokojné spomienky</p>
                        <h2 style="color: #327881; float: right; margin: 0;">Cenová ponuka</h2>
                        <p style="float: right; margin: 0;">Dátum: ${today}</p>
                        <div style="clear: both;"></div>
                    </div>

                    <div class="section">
                        <h3>Kontakt</h3>
                        <p><strong>Vladimír Seman</strong><br>
                        +421 951 553 464<br>
                        <EMAIL></p>
                    </div>

                    <div class="section">
                        <h3>Údaje o zákazníkovi</h3>
                        <p><strong>Meno:</strong> ${clientData.name}<br>
                        <strong>Telefón:</strong> ${clientData.phone}`;

                if (clientData.email) {
                    printContent += `<br><strong>Email:</strong> ${clientData.email}`;
                }
                if (clientData.address) {
                    printContent += `<br><strong>Adresa:</strong> ${clientData.address}`;
                }

                printContent += `</p>
                    </div>

                    <div class="section">
                        <h3>Vybrané služby</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Služba</th>
                                    <th>Typ hrobu</th>
                                    <th style="text-align: right;">Cena</th>
                                </tr>
                            </thead>
                            <tbody>`;

                graves.forEach((grave, index) => {
                    const price = PRICES[grave.service][grave.type];
                    const displayPrice = showDph ? price.sDph : price.bezDph;

                    printContent += `
                                <tr>
                                    <td>${SERVICE_TYPES[grave.service]}</td>
                                    <td>${GRAVE_TYPES[grave.type]}</td>
                                    <td style="text-align: right;">${displayPrice.toFixed(2)} €</td>
                                </tr>`;

                    if (grave.location) {
                        printContent += `
                                <tr>
                                    <td colspan="3" style="font-style: italic; color: #666; padding-left: 20px;">
                                        📍 Lokalita: ${grave.location}
                                    </td>
                                </tr>`;
                    }
                });

                printContent += `
                            </tbody>
                        </table>
                    </div>`;

                // Additional services
                if (additionalServices.sviatocne || additionalServices.pisma || additionalServices.impregnacia) {
                    printContent += `
                    <div class="section">
                        <h3>Doplnkové služby</h3>
                        <table>
                            <tbody>`;

                    if (additionalServices.sviatocne) {
                        printContent += `
                                <tr>
                                    <td>🎄 Sviatočné čistenie</td>
                                    <td style="text-align: right;">${showDph ? '59,99' : '49,99'} €</td>
                                </tr>`;
                    }

                    if (additionalServices.pisma) {
                        printContent += `
                                <tr>
                                    <td>✍️ Obnova písma</td>
                                    <td style="text-align: right;">${showDph ? '119,99' : '99,99'} €</td>
                                </tr>`;
                    }

                    if (additionalServices.impregnacia) {
                        printContent += `
                                <tr>
                                    <td>🛡️ Impregnácia kameňa</td>
                                    <td style="text-align: right;">${showDph ? '71,99' : '59,99'} €</td>
                                </tr>`;
                    }

                    printContent += `
                            </tbody>
                        </table>
                    </div>`;
                }

                // Price summary
                printContent += `
                    <div class="section">
                        <h3>Súhrn cien</h3>
                        <table>`;

                if (showDph) {
                    printContent += `
                            <tr>
                                <td>Základ dane:</td>
                                <td style="text-align: right;">${(subtotal / 1.2).toFixed(2)} €</td>
                            </tr>
                            <tr>
                                <td>DPH 20%:</td>
                                <td style="text-align: right;">${(subtotal - subtotal / 1.2).toFixed(2)} €</td>
                            </tr>`;
                }

                if (discount > 0) {
                    printContent += `
                            <tr>
                                <td>Zľava ${discount}%:</td>
                                <td style="text-align: right; color: red;">-${(subtotal * discount / 100).toFixed(2)} €</td>
                            </tr>`;
                }

                printContent += `
                            <tr style="font-weight: bold; font-size: 16px;">
                                <td>Celkom ${showDph ? 's DPH' : 'bez DPH'}:</td>
                                <td style="text-align: right;">${total.toFixed(2)} €</td>
                            </tr>
                        </table>
                    </div>`;

                // Notes
                if (notes.trim()) {
                    printContent += `
                    <div class="section">
                        <h3>📝 Poznámky</h3>
                        <p>${notes}</p>
                    </div>`;
                }

                printContent += `
                    <div class="footer">
                        <p><strong>🙏 Ďakujeme za Váš záujem o naše služby!</strong></p>
                        <p>Pre viac informácií nás kontaktujte na <strong>+421 951 553 464</strong> alebo <strong><EMAIL></strong></p>
                        <p style="font-style: italic;">Ponuka platná 30 dní od dátumu vystavenia</p>
                        <p><strong>eHroby - vytvárame pokojné spomienky</strong></p>
                    </div>
                </body>
                </html>`;

                // Open new window with print-friendly content
                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();

                // Auto-print after a short delay
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };



            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <h1><i className="fas fa-file-invoice"></i> eHroby - Generátor cenových ponúk</h1>
                            <p>Profesionálne služby starostlivosti o hrobové miesta</p>
                        </div>
                    </header>

                    <main className="main">
                        <div className="container">
                            <div className="form-section">
                                <h2><i className="fas fa-user"></i> Údaje klienta</h2>
                                <div className="form-grid">
                                    <div className="form-group">
                                        <label>Meno a priezvisko *</label>
                                        <input
                                            type="text"
                                            value={clientData.name}
                                            onChange={(e) => setClientData({...clientData, name: e.target.value})}
                                            placeholder="Zadajte meno a priezvisko"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Telefón *</label>
                                        <input
                                            type="tel"
                                            value={clientData.phone}
                                            onChange={(e) => setClientData({...clientData, phone: e.target.value})}
                                            placeholder="+421 xxx xxx xxx"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Email</label>
                                        <input
                                            type="email"
                                            value={clientData.email}
                                            onChange={(e) => setClientData({...clientData, email: e.target.value})}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Adresa hrobových miest</label>
                                        <input
                                            type="text"
                                            value={clientData.address}
                                            onChange={(e) => setClientData({...clientData, address: e.target.value})}
                                            placeholder="Cintorín, mesto"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="form-section">
                                <div className="section-header">
                                    <h2><i className="fas fa-cross"></i> Výber služieb</h2>
                                    <div className="price-toggle">
                                        <label className="toggle-switch">
                                            <input
                                                type="checkbox"
                                                checked={showDph}
                                                onChange={(e) => setShowDph(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                        <span>Zobraziť ceny {showDph ? 's DPH' : 'bez DPH'}</span>
                                    </div>
                                </div>

                                {graves.map((grave, index) => (
                                    <div key={grave.id} className="grave-item">
                                        <div className="grave-header">
                                            <h3>Hrob #{index + 1}</h3>
                                            {graves.length > 1 && (
                                                <button
                                                    className="btn-remove"
                                                    onClick={() => removeGrave(grave.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group">
                                                <label>Typ hrobu</label>
                                                <select
                                                    value={grave.type}
                                                    onChange={(e) => updateGrave(grave.id, 'type', e.target.value)}
                                                >
                                                    {Object.entries(GRAVE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Frekvencia služby</label>
                                                <select
                                                    value={grave.service}
                                                    onChange={(e) => updateGrave(grave.id, 'service', e.target.value)}
                                                >
                                                    {Object.entries(SERVICE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Lokalita hrobu</label>
                                                <input
                                                    type="text"
                                                    value={grave.location}
                                                    onChange={(e) => updateGrave(grave.id, 'location', e.target.value)}
                                                    placeholder="Napr. sektor A, rad 5, číslo 12"
                                                />
                                            </div>

                                            <div className="price-display">
                                                <span className="price">
                                                    {showDph
                                                        ? PRICES[grave.service][grave.type].sDph.toFixed(2)
                                                        : PRICES[grave.service][grave.type].bezDph.toFixed(2)
                                                    } €
                                                </span>
                                                <small>{showDph ? 's DPH' : 'bez DPH'}</small>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <button className="btn-add" onClick={addGrave}>
                                    <i className="fas fa-plus"></i> Pridať ďalší hrob
                                </button>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-plus-circle"></i> Doplnkové služby</h2>
                                <div className="additional-services">
                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.sviatocne}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                sviatocne: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Sviatočné čistenie</span>
                                        <span className="service-price">
                                            {showDph ? '59,99' : '49,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.pisma}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                pisma: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Obnova písma</span>
                                        <span className="service-price">
                                            {showDph ? '119,99' : '99,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.impregnacia}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                impregnacia: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Impregnácia kameňa</span>
                                        <span className="service-price">
                                            {showDph ? '71,99' : '59,99'} €
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-percentage"></i> Zľava</h2>
                                <div className="discount-section">
                                    <label className="form-group">
                                        <span>Zľava (%)</span>
                                        <select
                                            value={discount}
                                            onChange={(e) => setDiscount(Number(e.target.value))}
                                        >
                                            <option value={0}>Bez zľavy</option>
                                            <option value={5}>5% zľava</option>
                                            <option value={10}>10% zľava</option>
                                            <option value={15}>15% zľava</option>
                                        </select>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-sticky-note"></i> Poznámky</h2>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Špeciálne požiadavky alebo poznámky..."
                                    rows="4"
                                ></textarea>
                            </div>

                            <div className="summary-section">
                                <div className="total-price">
                                    <div className="price-breakdown">
                                        <div className="subtotal">
                                            <span>Medzisúčet:</span>
                                            <span>{calculateSubtotal().toFixed(2)} €</span>
                                        </div>
                                        {discount > 0 && (
                                            <div className="discount-amount">
                                                <span>Zľava ({discount}%):</span>
                                                <span className="discount-value">-{(calculateSubtotal() * discount / 100).toFixed(2)} €</span>
                                            </div>
                                        )}
                                        <div className="total">
                                            <h2>Celková suma: {calculateTotal().toFixed(2)} €</h2>
                                            <p>{showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'}</p>
                                        </div>
                                    </div>

                                    {showDph && (
                                        <div className="tax-breakdown">
                                            <div>Základ dane: {(calculateTotal() / 1.2).toFixed(2)} €</div>
                                            <div>DPH (20%): {(calculateTotal() - calculateTotal() / 1.2).toFixed(2)} €</div>
                                        </div>
                                    )}
                                </div>

                                <button
                                    className="btn-generate"
                                    onClick={generatePDF}
                                    disabled={!clientData.name || !clientData.phone}
                                >
                                    <i className="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                            </div>
                        </div>
                    </main>

                    <footer className="footer">
                        <div className="container">
                            <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
                        </div>
                    </footer>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
